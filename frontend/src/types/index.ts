export interface User {
  id: string;
  address: string;
  balance: number;
  vestedTokens: number;
  totalPurchased: number;
  referralCode: string;
  referralCount: number;
  isConnected: boolean;
}

export interface Package {
  id: string;
  name: string;
  minAmount: number;
  maxAmount: number;
  vestingPeriod: number; // Duration in months (all packages use 60 months = 5 years)
  interestRate: number; // APY percentage
  description: string;
  features: string[];
  popular?: boolean;
  contractData?: any; // Raw contract data including vestSplitBps (vesting allocation percentage)
}

export interface Transaction {
  id: string;
  type: 'purchase' | 'claim' | 'referral';
  amount: number;
  token: string;
  timestamp: Date;
  status: 'pending' | 'completed' | 'failed';
  hash?: string;
}

export interface VestingSchedule {
  id: string;
  packageId: string;
  totalAmount: number;
  claimedAmount: number;
  nextClaimDate: Date;
  claimableAmount: number;
  completionDate: Date;
}

export interface AdminStats {
  totalUsers: number;
  totalValueLocked: number;
  totalTransactions: number;
  activePackages: number;
  pendingWithdrawals: number;
}